import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/responsive/responsive_padding.dart';
import '../models/class_model.dart';
import '../models/activity_model.dart';
import '../mock/mock_classes.dart';
import '../mock/mock_activities.dart';
import '../widgets/classroom_header.dart';
import '../widgets/navigation_grid.dart';
import '../widgets/recent_activity_feed.dart';
import '../enums/activity_type.dart';

/// Screen displaying detailed information about a specific classroom
class ClassroomDetailScreen extends StatefulWidget {
  /// ID of the classroom to display
  final String classroomId;

  const ClassroomDetailScreen({super.key, required this.classroomId});

  @override
  State<ClassroomDetailScreen> createState() => _ClassroomDetailScreenState();
}

class _ClassroomDetailScreenState extends State<ClassroomDetailScreen> {
  /// Current classroom data
  ClassModel? _classroom;

  /// Recent activities for this classroom
  List<ActivityModel> _recentActivities = [];

  @override
  void initState() {
    super.initState();
    _loadClassroomData();
  }

  /// Load classroom and activity data
  void _loadClassroomData() {
    // Find classroom by ID
    try {
      _classroom = mockClassesList.firstWhere(
        (classroom) => classroom.id == widget.classroomId,
      );

      // Get recent activities for this classroom (max 3 days or 10 items)
      final now = DateTime.now();
      final threeDaysAgo = now.subtract(const Duration(days: 3));

      _recentActivities = mockActivitiesList
          .where(
            (activity) =>
                activity.classroomId == widget.classroomId &&
                activity.createdAt.isAfter(threeDaysAgo),
          )
          .take(10)
          .toList();

      setState(() {});
    } catch (e) {
      // Classroom not found
      debugPrint('Classroom not found: ${widget.classroomId}');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show loading or error state if classroom not found
    if (_classroom == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Classroom')),
        body: const Center(child: Text('Classroom not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_classroom!.name, style: theme.textTheme.titleLarge),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          // TODO: Add classroom settings/options menu
          IconButton(
            icon: const Icon(Symbols.more_vert),
            onPressed: () {
              // TODO: Show classroom options menu
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Classroom options coming soon')),
              );
            },
          ),
        ],
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Classroom header with info
              ClassroomHeader(classroom: _classroom!),

              SizedBox(height: 24.h),

              // Navigation grid (3x2)
              NavigationGrid(
                classroomId: widget.classroomId,
                onNavigate: _handleNavigation,
              ),

              SizedBox(height: 24.h),

              // Recent activity feed section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Activity',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (_recentActivities.isNotEmpty)
                    TextButton(
                      onPressed: () => _navigateToActivityFeed(),
                      child: const Text('View All'),
                    ),
                ],
              ),

              SizedBox(height: 12.h),

              // Recent activities list
              RecentActivityFeed(
                activities: _recentActivities,
                onActivityTap: _handleActivityTap,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle navigation to different sections
  void _handleNavigation(String section) {
    switch (section) {
      case 'homework':
        // Navigate to homework list with classroom filter
        context.pushNamed(
          RouteNames.homeworkList,
          queryParameters: {'classroomId': widget.classroomId},
        );
        break;
      case 'notes':
        // TODO: Navigate to notes screen
        _showComingSoonMessage('Notes');
        break;
      case 'quizzes':
        // TODO: Navigate to quizzes screen
        _showComingSoonMessage('Quizzes');
        break;
      case 'notices':
        // TODO: Navigate to notices screen
        _showComingSoonMessage('Notices');
        break;
      case 'discussions':
        context.pushNamed(
          RouteNames.classroomDiscussion,
          pathParameters: {'id': widget.classroomId},
        );
        break;
      case 'attendance':
        // TODO: Navigate to attendance screen
        _showComingSoonMessage('Attendance');
        break;
      case 'digital_library':
        context.pushNamed(
          RouteNames.classroomResources,
          pathParameters: {'id': widget.classroomId},
        );
        break;
    }
  }

  /// Navigate to full activity feed
  void _navigateToActivityFeed() {
    context.pushNamed(
      RouteNames.activityFeed,
      pathParameters: {'id': widget.classroomId},
    );
  }

  /// Handle activity item tap
  void _handleActivityTap(ActivityModel activity) {
    if (!activity.type.isClickable) return;

    switch (activity.type) {
      case ActivityType.homework:
        if (activity.referenceId != null) {
          context.pushNamed(
            RouteNames.homeworkDetail,
            pathParameters: {'id': activity.referenceId!},
          );
        }
        break;
      case ActivityType.quiz:
        // TODO: Navigate to quiz detail
        _showComingSoonMessage('Quiz details');
        break;
      case ActivityType.digitalLibrary:
        // TODO: Navigate to resource detail
        _showComingSoonMessage('Resource details');
        break;
      case ActivityType.discussion:
        if (activity.referenceId != null) {
          // TODO: Navigate to specific discussion thread
          _showComingSoonMessage('Discussion thread');
        }
        break;
      default:
        break;
    }
  }

  /// Show coming soon message for unimplemented features
  void _showComingSoonMessage(String feature) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('$feature feature coming soon')));
  }
}
