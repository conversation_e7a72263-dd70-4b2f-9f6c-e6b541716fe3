# Classroom Module — ScholaraHub Student App

The **Classroom module** is the central hub for students to engage with individual subjects, teachers, and class content.  A classroom is a group of students and teachers and maybe parents, allowing access to tasks, notices, discussions, shared resources, and recent activities for that particular class. A classroom can be classified as anything including but not limited to:

- A classroom of a specific subject such as physics, accounts, English etc.
- A section of a subject such as physics 1, physics 2, accounts 1, accounts 2 etc.
- A grade such as 10th, 11th, 12th
- A club such as chess club, debate club, robotics club etc.
- A team such as a sports team, a drama team, a music team etc.
- A group such as a project group
- A classroom may or may not have a teacher. For example, a project group may not have a teacher.
- Each student has to join atleast one classroom which will be his/her main class. This main class will be used to determine the student's grade, section, and other important information.

These are just a few examples, there can be many more types of classrooms. Also each type of classroom may or may not have further types and settings.

I am noticing that you think there is a difference between class and a classroom which is not the case.

---

## Overview

- Students see a list of all their enrolled classrooms.
- Each classroom acts as a gateway to:
  - Homework
  - Notes
  - Notices
  - Quizzes
  - Discussions
  - Attendance
  - Digital Library (also called as shared resources but formal feature name is digital library)

An important thing to note here is that most of these feature have not been developed yet, so connect only those which have been developed. Create dummy screens and widgets for those which have not been developed yet.

---

## Feature Breakdown

### 1. 📋 Rooms List Screen

> Path: `classrooms/screens/classrooms_list_screen.dart`

Displays all the classrooms a student is enrolled in.

#### Key UI Elements:
- Subject title
- Instructor/mentor/teacher name & section (optional in case of student only classrooms or the name of the student admin/leader)
- Task/status chips (e.g., “Assignment due today”, “Quiz tomorrow”) (an alert system will be created later, add todo for now and connect it later)
- Major Filters: **All**, **Core Subjects**, **Electives**, **Clubs**, **Teams**, **Project Groups**
- Other filters and sorting options should also be there
- Search bar to find a classroom
- There will be an option to choose main color for the class so a thin vertical bar on the left side of the card will be colored with the main color of the class.
- There will be an option to choose icon/profile picture for the class so the it will be displayed on the card as avatar (top left). Create logic accordingly, either one of them will be used, in case of both being provided, use the picture. There will be scope in case I want to keep neither so both can be null also.
- A menu icon on the top right corner of the card to show more options like edit, delete/leave, archive etc.
- A button to create a new classroom - FAB (a student may or may not be able to create a class depending on the school and students main class)

---

### 2. Classroom Detail Screen

> Path: `classrooms/screens/classroom_detail_screen.dart`

This is the landing page for any classroom. It summarizes all the components of the subject. This page will be the core of this feature

#### UI Sections:
- Subject header (name, instructor/admin, type of classroom, icon/picture if present)
- Navigation grid (3x2 grid) (Homework, Notes, Quizzes, Notices, Discussions, Attendance, digital library) 
- Recent activity feed: timeline of updates (e.g., assignment added, quiz posted)
    - This activity feed will act as the main timeline. All the homework, notifications, notices, quizzes, attendance, digital library etc. will be visible on this feed.
    - You can also think of it as a feed of all the activities of the classroom. Can be also called as posts, classroom events, activity feed, classroom feed, classroom updates etc.
    - This will also have a dedicated page including filters and sorting options.
    - This widget on the landing page will be a scrollable list of the recent activities (atmost 3 days or 10 items).
    - Each activity will be a tile with a title, an icon on left side representing the type of activity, and a timestamp. clicking on the tile will take the user to the respective screen of the activity. For example, if it is a homework, it will take the user to the homework detail screen. If it is a quiz, it will take the user to the quiz detail screen. If it is a discussion, it will take the user to the discussion detail screen
    - Note that discussion won't be displayed here, it will be displayed on the discussion screen.
    - Also note that important announcements by teacher or admin or any authorised student will be displayed here. These announcements are not same as notices or discussion, but a stand alone, kind of a message for all thing.

---

### 3. Activity Feed Screen

> Path: `classrooms/screens/activity_feed_screen.dart`

- This is the dedicated page for the activity feed.
- It will have all the activities of the classroom.
- It will have filters and sorting options.
- It will also have a search bar to search for specific activities. 
- There will be a button to add new announcement. (FAB)
- Only a teacher, admin or an authorised member/student is allowed to create announcement (depending upon setting of the classroom).
- each activity will be a card without any elevation or borders and contain a title, a 2-3 line description (if applicable), an icon on left side representing the type of activity, and a timestamp. clicking on the tile will take the user to the respective screen of the activity.

#### Supports:
- Homework
- Notices
- Announcements
- Notifications
- Quizzes
- Reminders
- Digital Library (also includes notes)

---

### 4. Resources/Notes Screen

> Path: `classrooms/screens/resources_screen.dart`

Library of subject-specific resources uploaded by teachers. This feature is linked to Digital Library feature Which in to be developed later.

#### Filters:
- Tabs: All, Recent, Important, Uploaded
- Resource Types: PDFs, Videos, Links, Notes
- Tags: Chapter, Topic, Practice, NCERT, External, Chemistry, Biology etc.

Each resource card shows:
- Title + uploaded by + date
- Type icon (PDF, video, link)
- Labels/tags
- File preview or link

---

### 5. Discussion Screen

> Path: `classrooms/screens/discussion_screen.dart`

Interactive forum for class discussions, doubts, and collaboration.

#### Post Types:
- Instructor Announcements
- Homework Doubts
- Shared Resources
- General Questions

#### Features:
- Instructor labels
- File attachments (e.g., PDFs)
- Like and reply support
- Nested reply threads

---


