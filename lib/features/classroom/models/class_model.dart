import '../enums/classroom_type.dart';

/// Enhanced model representing a classroom in the Scholara student app
/// Supports various classroom types, customization, and activity management
class ClassModel {
  /// Unique identifier for the classroom
  final String id;

  /// Name of the classroom (e.g., "Mathematics 10A", "Chess Club")
  final String name;

  /// Subject of the classroom (e.g., "Mathematics", "Physics")
  /// Can be null for non-academic classrooms like clubs or project groups
  final String? subject;

  /// Optional description of the classroom
  /// Provides additional context about the classroom's purpose, goals, or content
  final String? description;

  /// Type of classroom (core subject, elective, club, team, etc.)
  final ClassroomType type;

  /// ID of the teacher/instructor who manages this classroom
  /// Can be null for student-only classrooms like project groups
  final String? teacherId;

  /// Name of the teacher/instructor (for display purposes)
  final String? teacherName;

  /// ID of the student admin/leader (for student-led classrooms)
  /// Used when there's no teacher or for additional leadership
  final String? studentAdminId;

  /// Name of the student admin/leader (for display purposes)
  final String? studentAdminName;

  /// List of student user IDs enrolled in this classroom
  final List<String> studentIds;

  /// Custom color for the classroom (hex string)
  /// Used for the vertical bar on classroom cards
  final String? customColor;

  /// Icon identifier for the classroom
  /// Used when no profile picture is provided
  final String? iconId;

  /// Profile picture URL for the classroom
  /// Takes precedence over iconId when both are provided
  final String? profilePictureUrl;

  /// Whether this is the student's main classroom
  /// Used to determine grade, section, and other important information
  final bool isMainClass;

  /// Grade level associated with this classroom (e.g., "10", "11", "12")
  final String? gradeLevel;

  /// Section identifier (e.g., "A", "B", "C")
  final String? section;

  /// When the classroom was created
  final DateTime? createdAt;

  /// Whether the classroom is currently active
  final bool isActive;

  /// Additional settings and permissions for the classroom
  final Map<String, dynamic>? settings;

  const ClassModel({
    required this.id,
    required this.name,
    this.subject,
    this.description,
    required this.type,
    this.teacherId,
    this.teacherName,
    this.studentAdminId,
    this.studentAdminName,
    required this.studentIds,
    this.customColor,
    this.iconId,
    this.profilePictureUrl,
    this.isMainClass = false,
    this.gradeLevel,
    this.section,
    this.createdAt,
    this.isActive = true,
    this.settings,
  });

  /// Create a ClassModel from JSON
  factory ClassModel.fromJson(Map<String, dynamic> json) {
    return ClassModel(
      id: json['id'] as String,
      name: json['name'] as String,
      subject: json['subject'] as String?,
      description: json['description'] as String?,
      type: ClassroomTypeExtension.fromString(
        json['type'] as String? ?? 'core_subject',
      ),
      teacherId: json['teacherId'] as String?,
      teacherName: json['teacherName'] as String?,
      studentAdminId: json['studentAdminId'] as String?,
      studentAdminName: json['studentAdminName'] as String?,
      studentIds: List<String>.from(json['studentIds'] as List),
      customColor: json['customColor'] as String?,
      iconId: json['iconId'] as String?,
      profilePictureUrl: json['profilePictureUrl'] as String?,
      isMainClass: json['isMainClass'] as bool? ?? false,
      gradeLevel: json['gradeLevel'] as String?,
      section: json['section'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      isActive: json['isActive'] as bool? ?? true,
      settings: json['settings'] as Map<String, dynamic>?,
    );
  }

  /// Convert ClassModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'description': description,
      'type': type.value,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'studentAdminId': studentAdminId,
      'studentAdminName': studentAdminName,
      'studentIds': studentIds,
      'customColor': customColor,
      'iconId': iconId,
      'profilePictureUrl': profilePictureUrl,
      'isMainClass': isMainClass,
      'gradeLevel': gradeLevel,
      'section': section,
      'createdAt': createdAt?.toIso8601String(),
      'isActive': isActive,
      'settings': settings,
    };
  }

  /// Create a copy of this ClassModel with updated fields
  ClassModel copyWith({
    String? id,
    String? name,
    String? subject,
    String? description,
    ClassroomType? type,
    String? teacherId,
    String? teacherName,
    String? studentAdminId,
    String? studentAdminName,
    List<String>? studentIds,
    String? customColor,
    String? iconId,
    String? profilePictureUrl,
    bool? isMainClass,
    String? gradeLevel,
    String? section,
    DateTime? createdAt,
    bool? isActive,
    Map<String, dynamic>? settings,
  }) {
    return ClassModel(
      id: id ?? this.id,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      description: description ?? this.description,
      type: type ?? this.type,
      teacherId: teacherId ?? this.teacherId,
      teacherName: teacherName ?? this.teacherName,
      studentAdminId: studentAdminId ?? this.studentAdminId,
      studentAdminName: studentAdminName ?? this.studentAdminName,
      studentIds: studentIds ?? this.studentIds,
      customColor: customColor ?? this.customColor,
      iconId: iconId ?? this.iconId,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      isMainClass: isMainClass ?? this.isMainClass,
      gradeLevel: gradeLevel ?? this.gradeLevel,
      section: section ?? this.section,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      settings: settings ?? this.settings,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClassModel &&
        other.id == id &&
        other.name == name &&
        other.subject == subject &&
        other.description == description &&
        other.type == type &&
        other.teacherId == teacherId &&
        other.teacherName == teacherName &&
        other.studentAdminId == studentAdminId &&
        other.studentAdminName == studentAdminName &&
        _listEquals(other.studentIds, studentIds) &&
        other.customColor == customColor &&
        other.iconId == iconId &&
        other.profilePictureUrl == profilePictureUrl &&
        other.isMainClass == isMainClass &&
        other.gradeLevel == gradeLevel &&
        other.section == section &&
        other.createdAt == createdAt &&
        other.isActive == isActive &&
        _mapEquals(other.settings, settings);
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      subject,
      description,
      type,
      teacherId,
      teacherName,
      studentAdminId,
      studentAdminName,
      Object.hashAll(studentIds),
      customColor,
      iconId,
      profilePictureUrl,
      isMainClass,
      gradeLevel,
      section,
      createdAt,
      isActive,
      settings != null ? Object.hashAll(settings!.entries) : null,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'ClassModel(id: $id, name: $name, type: ${type.label}, subject: $subject, description: $description, studentCount: ${studentIds.length})';
  }

  /// Helper methods for classroom functionality

  /// Returns the display name for the instructor/admin
  String? get instructorDisplayName {
    if (teacherName != null) return teacherName;
    if (studentAdminName != null) return studentAdminName;
    return null;
  }

  /// Returns the effective color for the classroom (custom or default)
  String get effectiveColor {
    if (customColor != null) return customColor!;
    // Convert Color to hex string using toARGB32
    final color = type.defaultColor;
    return '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2)}';
  }

  /// Returns whether this classroom has an instructor (teacher or student admin)
  bool get hasInstructor {
    return teacherId != null || studentAdminId != null;
  }

  /// Returns whether this classroom allows student creation of content
  bool get allowsStudentContent {
    // Project groups and clubs typically allow more student participation
    return type == ClassroomType.projectGroup || type == ClassroomType.club;
  }
}
