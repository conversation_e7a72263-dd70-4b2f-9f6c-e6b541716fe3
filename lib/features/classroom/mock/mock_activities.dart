import 'package:faker/faker.dart';
import 'package:uuid/uuid.dart';
import '../models/activity_model.dart';
import '../enums/activity_type.dart';
import 'mock_classes.dart';

const _uuid = Uuid();

/// Mock data for classroom activities with realistic data generation
final List<ActivityModel> mockActivitiesList = _generateMockActivities();

/// Generate realistic mock activity data for all classrooms
List<ActivityModel> _generateMockActivities() {
  final faker = Faker();
  final activities = <ActivityModel>[];

  // Get all mock classrooms
  final classrooms = mockClassesList;

  // Generate activities for each classroom
  for (final classroom in classrooms) {
    // Generate 3-8 activities per classroom
    final activityCount = faker.randomGenerator.integer(8, min: 3);
    
    for (int i = 0; i < activityCount; i++) {
      // Randomly select activity type
      final activityTypes = ActivityType.values;
      final activityType = activityTypes[faker.randomGenerator.integer(activityTypes.length)];
      
      // Generate activity based on type
      final activity = _generateActivityByType(
        faker,
        classroom.id,
        activityType,
        classroom,
      );
      
      if (activity != null) {
        activities.add(activity);
      }
    }
  }

  // Sort activities by creation date (newest first)
  activities.sort((a, b) => b.createdAt.compareTo(a.createdAt));

  return activities;
}

/// Generate a specific activity based on its type
ActivityModel? _generateActivityByType(
  Faker faker,
  String classroomId,
  ActivityType type,
  dynamic classroom,
) {
  final now = DateTime.now();
  final createdAt = now.subtract(
    Duration(
      days: faker.randomGenerator.integer(30),
      hours: faker.randomGenerator.integer(24),
      minutes: faker.randomGenerator.integer(60),
    ),
  );

  // Determine who created the activity
  String createdById;
  String createdByName;
  
  if (type.canBeCreatedByStudents) {
    // For discussions, sometimes students create them
    if (faker.randomGenerator.boolean()) {
      createdById = classroom.studentIds[faker.randomGenerator.integer(classroom.studentIds.length)];
      createdByName = '${faker.person.firstName()} ${faker.person.lastName()}';
    } else {
      createdById = classroom.teacherId ?? classroom.studentAdminId ?? 'unknown';
      createdByName = classroom.instructorDisplayName ?? 'Unknown';
    }
  } else {
    // Only teachers/admins can create these
    createdById = classroom.teacherId ?? classroom.studentAdminId ?? 'unknown';
    createdByName = classroom.instructorDisplayName ?? 'Unknown';
  }

  switch (type) {
    case ActivityType.homework:
      return ActivityModel(
        id: _uuid.v4(),
        classroomId: classroomId,
        type: type,
        title: _generateHomeworkTitle(faker, classroom.subject),
        description: faker.randomGenerator.boolean() 
            ? faker.lorem.sentence()
            : null,
        createdById: createdById,
        createdByName: createdByName,
        createdAt: createdAt,
        referenceId: _uuid.v4(), // Would be actual homework ID
        isPinned: faker.randomGenerator.boolean(5), // 5% chance of being pinned
      );

    case ActivityType.notice:
      return ActivityModel(
        id: _uuid.v4(),
        classroomId: classroomId,
        type: type,
        title: _generateNoticeTitle(faker),
        description: faker.lorem.sentences(2).join(' '),
        createdById: createdById,
        createdByName: createdByName,
        createdAt: createdAt,
        isPinned: faker.randomGenerator.boolean(15), // 15% chance of being pinned
      );

    case ActivityType.announcement:
      return ActivityModel(
        id: _uuid.v4(),
        classroomId: classroomId,
        type: type,
        title: _generateAnnouncementTitle(faker),
        description: faker.lorem.sentences(3).join(' '),
        createdById: createdById,
        createdByName: createdByName,
        createdAt: createdAt,
        isPinned: faker.randomGenerator.boolean(25), // 25% chance of being pinned
      );

    case ActivityType.quiz:
      return ActivityModel(
        id: _uuid.v4(),
        classroomId: classroomId,
        type: type,
        title: _generateQuizTitle(faker, classroom.subject),
        description: 'Duration: ${faker.randomGenerator.integer(120, min: 30)} minutes',
        createdById: createdById,
        createdByName: createdByName,
        createdAt: createdAt,
        referenceId: _uuid.v4(), // Would be actual quiz ID
        isPinned: faker.randomGenerator.boolean(10), // 10% chance of being pinned
      );

    case ActivityType.digitalLibrary:
      return ActivityModel(
        id: _uuid.v4(),
        classroomId: classroomId,
        type: type,
        title: _generateResourceTitle(faker, classroom.subject),
        description: faker.lorem.sentence(),
        createdById: createdById,
        createdByName: createdByName,
        createdAt: createdAt,
        referenceId: _uuid.v4(), // Would be actual resource ID
      );

    case ActivityType.discussion:
      return ActivityModel(
        id: _uuid.v4(),
        classroomId: classroomId,
        type: type,
        title: _generateDiscussionTitle(faker, classroom.subject),
        description: faker.lorem.sentences(2).join(' '),
        createdById: createdById,
        createdByName: createdByName,
        createdAt: createdAt,
        referenceId: _uuid.v4(), // Would be actual discussion ID
      );

    case ActivityType.attendance:
    case ActivityType.notification:
    case ActivityType.reminder:
      // These are typically system-generated, less common in mock data
      if (faker.randomGenerator.boolean(30)) { // 30% chance to generate
        return ActivityModel(
          id: _uuid.v4(),
          classroomId: classroomId,
          type: type,
          title: _generateSystemTitle(faker, type),
          description: faker.lorem.sentence(),
          createdById: 'system',
          createdByName: 'System',
          createdAt: createdAt,
        );
      }
      return null;
  }
}

String _generateHomeworkTitle(Faker faker, String? subject) {
  final topics = {
    'Mathematics': ['Algebra Problems', 'Geometry Exercises', 'Calculus Assignment', 'Statistics Worksheet'],
    'Physics': ['Motion Problems', 'Electricity Lab', 'Optics Assignment', 'Thermodynamics Worksheet'],
    'Chemistry': ['Chemical Equations', 'Organic Chemistry Lab', 'Periodic Table Quiz', 'Molecular Structure'],
    'Biology': ['Cell Structure', 'Genetics Assignment', 'Ecosystem Study', 'Human Anatomy'],
    'English Literature': ['Essay Writing', 'Poetry Analysis', 'Novel Review', 'Grammar Exercises'],
    'History': ['World War Analysis', 'Ancient Civilizations', 'Historical Timeline', 'Document Analysis'],
    'Geography': ['Map Reading', 'Climate Study', 'Population Analysis', 'Natural Resources'],
    'Computer Science': ['Programming Assignment', 'Algorithm Design', 'Database Project', 'Web Development'],
  };

  final subjectTopics = topics[subject] ?? ['Assignment', 'Worksheet', 'Project', 'Exercise'];
  return subjectTopics[faker.randomGenerator.integer(subjectTopics.length)];
}

String _generateNoticeTitle(Faker faker) {
  final notices = [
    'Class Schedule Change',
    'Exam Date Announcement',
    'Holiday Notice',
    'Parent-Teacher Meeting',
    'Field Trip Information',
    'Library Hours Update',
    'Uniform Guidelines',
    'Fee Payment Reminder',
  ];
  return notices[faker.randomGenerator.integer(notices.length)];
}

String _generateAnnouncementTitle(Faker faker) {
  final announcements = [
    'Welcome to New Semester',
    'Achievement Recognition',
    'Important Deadline Reminder',
    'Class Performance Update',
    'Upcoming Events',
    'Study Tips and Guidelines',
    'Resource Sharing',
    'Motivational Message',
  ];
  return announcements[faker.randomGenerator.integer(announcements.length)];
}

String _generateQuizTitle(Faker faker, String? subject) {
  final quizTypes = ['Weekly Quiz', 'Unit Test', 'Pop Quiz', 'Practice Test', 'Assessment'];
  final quizType = quizTypes[faker.randomGenerator.integer(quizTypes.length)];
  return subject != null ? '$subject $quizType' : quizType;
}

String _generateResourceTitle(Faker faker, String? subject) {
  final resourceTypes = ['Study Notes', 'Reference Material', 'Practice Papers', 'Video Lecture', 'E-book'];
  final resourceType = resourceTypes[faker.randomGenerator.integer(resourceTypes.length)];
  return subject != null ? '$subject $resourceType' : resourceType;
}

String _generateDiscussionTitle(Faker faker, String? subject) {
  final discussions = [
    'Doubt Clarification',
    'Topic Discussion',
    'Study Group Formation',
    'Project Collaboration',
    'Concept Explanation',
    'Question and Answer',
    'Peer Help',
    'Group Study',
  ];
  final discussion = discussions[faker.randomGenerator.integer(discussions.length)];
  return subject != null ? '$subject: $discussion' : discussion;
}

String _generateSystemTitle(Faker faker, ActivityType type) {
  switch (type) {
    case ActivityType.attendance:
      return 'Attendance Marked';
    case ActivityType.notification:
      return 'System Notification';
    case ActivityType.reminder:
      return 'Upcoming Deadline Reminder';
    default:
      return 'System Update';
  }
}
