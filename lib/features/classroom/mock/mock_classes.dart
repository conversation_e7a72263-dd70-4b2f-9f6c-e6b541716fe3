import 'package:faker/faker.dart';
import 'package:uuid/uuid.dart';
import '../models/class_model.dart';
import '../enums/classroom_type.dart';

const _uuid = Uuid();

/// Mock data for classes with realistic data generation
final List<ClassModel> mockClassesList = _generateMockClasses();

/// Generate realistic mock classroom data
List<ClassModel> _generateMockClasses() {
  final faker = Faker();
  final classes = <ClassModel>[];

  // Define realistic subjects and grade levels
  final coreSubjects = [
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English Literature',
    'History',
    'Geography',
  ];

  final electiveSubjects = [
    'Computer Science',
    'Art',
    'Music',
    'Psychology',
    'Economics',
  ];

  final clubs = [
    'Chess Club',
    'Debate Club',
    'Robotics Club',
    'Drama Club',
    'Photography Club',
  ];

  final teams = [
    'Basketball Team',
    'Football Team',
    'Cricket Team',
    'Volleyball Team',
  ];

  final gradeLevels = ['10', '11', '12'];
  final sections = ['A', 'B', 'C'];

  // Define custom colors for variety
  final customColors = [
    '#FF5722',
    '#E91E63',
    '#9C27B0',
    '#673AB7',
    '#3F51B5',
    '#2196F3',
    '#00BCD4',
    '#009688',
    '#4CAF50',
    '#8BC34A',
    '#CDDC39',
    '#FFC107',
    '#FF9800',
    '#795548',
    '#607D8B',
  ];

  // Generate teacher pool with realistic names
  final teachers = List.generate(15, (index) {
    final titles = ['Dr.', 'Prof.', 'Ms.', 'Mr.'];
    final title = titles[faker.randomGenerator.integer(titles.length)];
    return {
      'id': _uuid.v4(),
      'name': '$title ${faker.person.firstName()} ${faker.person.lastName()}',
    };
  });

  // Generate student pool with current user included
  final currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
  final students = [currentUserId, ...List.generate(49, (index) => _uuid.v4())];

  // Create core subject classes (5 classes)
  for (int i = 0; i < 5; i++) {
    final subject = coreSubjects[i % coreSubjects.length];
    final gradeLevel =
        gradeLevels[faker.randomGenerator.integer(gradeLevels.length)];
    final section = sections[faker.randomGenerator.integer(sections.length)];
    final teacher = teachers[i % teachers.length];
    final customColor =
        customColors[faker.randomGenerator.integer(customColors.length)];

    // Generate realistic student enrollment (15-25 students per class)
    final studentCount = faker.randomGenerator.integer(25, min: 15);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: '$subject $gradeLevel$section',
        subject: subject,
        type: ClassroomType.coreSubject,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        customColor: faker.randomGenerator.boolean() ? customColor : null,
        gradeLevel: gradeLevel,
        section: section,
        isMainClass: i == 0, // First class is main class
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(200, min: 30)),
        ),
        isActive: true,
      ),
    );
  }

  // Create elective classes (2 classes)
  for (int i = 0; i < 2; i++) {
    final subject = electiveSubjects[i % electiveSubjects.length];
    final gradeLevel =
        gradeLevels[faker.randomGenerator.integer(gradeLevels.length)];
    final teacher = teachers[(i + 5) % teachers.length];
    final customColor =
        customColors[faker.randomGenerator.integer(customColors.length)];

    final studentCount = faker.randomGenerator.integer(20, min: 10);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: '$subject $gradeLevel',
        subject: subject,
        type: ClassroomType.elective,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        customColor: faker.randomGenerator.boolean() ? customColor : null,
        gradeLevel: gradeLevel,
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(150, min: 20)),
        ),
        isActive: true,
      ),
    );
  }

  // Create club classes (2 clubs)
  for (int i = 0; i < 2; i++) {
    final clubName = clubs[i % clubs.length];
    final customColor =
        customColors[faker.randomGenerator.integer(customColors.length)];

    // Some clubs have teacher supervisors, some have student leaders
    final hasTeacher = faker.randomGenerator.boolean();
    final teacher = hasTeacher ? teachers[(i + 7) % teachers.length] : null;
    final studentLeader = !hasTeacher
        ? {
            'id': students[faker.randomGenerator.integer(students.length)],
            'name': '${faker.person.firstName()} ${faker.person.lastName()}',
          }
        : null;

    final studentCount = faker.randomGenerator.integer(15, min: 8);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: clubName,
        type: ClassroomType.club,
        teacherId: teacher?['id'],
        teacherName: teacher?['name'],
        studentAdminId: studentLeader?['id'],
        studentAdminName: studentLeader?['name'],
        studentIds: classStudents,
        customColor: customColor,
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(100, min: 10)),
        ),
        isActive: true,
      ),
    );
  }

  // Create team classes (1 team)
  final teamName = teams[0];
  final customColor =
      customColors[faker.randomGenerator.integer(customColors.length)];
  final coach = teachers[9 % teachers.length];

  final studentCount = faker.randomGenerator.integer(12, min: 8);
  final classStudents = <String>[];
  final shuffledStudents = List<String>.from(students)..shuffle();

  for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
    classStudents.add(shuffledStudents[j]);
  }

  classes.add(
    ClassModel(
      id: _uuid.v4(),
      name: teamName,
      type: ClassroomType.team,
      teacherId: coach['id']!,
      teacherName: coach['name']!,
      studentIds: classStudents,
      customColor: customColor,
      createdAt: DateTime.now().subtract(
        Duration(days: faker.randomGenerator.integer(80, min: 5)),
      ),
      isActive: true,
    ),
  );

  return classes;
}
