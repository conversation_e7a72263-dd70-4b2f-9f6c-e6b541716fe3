import 'package:faker/faker.dart';
import 'package:uuid/uuid.dart';
import '../models/class_model.dart';
import '../enums/classroom_type.dart';

const _uuid = Uuid();

/// Mock data for classes with realistic data generation
final List<ClassModel> mockClassesList = _generateMockClasses();

/// Generate realistic mock classroom data
List<ClassModel> _generateMockClasses() {
  final faker = Faker();
  final classes = <ClassModel>[];

  // Define realistic subjects and grade levels
  final coreSubjects = [
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English Literature',
    'History',
    'Geography',
  ];

  final electiveSubjects = [
    'Computer Science',
    'Art',
    'Music',
    'Psychology',
    'Economics',
  ];

  final clubs = [
    'Chess Club',
    'Debate Club',
    'Robotics Club',
    'Drama Club',
    'Photography Club',
  ];

  final teams = [
    'Basketball Team',
    'Football Team',
    'Cricket Team',
    'Volleyball Team',
  ];

  final gradeLevels = ['10', '11', '12'];
  final sections = ['A', 'B', 'C'];

  // Define custom colors for variety
  final customColors = [
    '#FF5722',
    '#E91E63',
    '#9C27B0',
    '#673AB7',
    '#3F51B5',
    '#2196F3',
    '#00BCD4',
    '#009688',
    '#4CAF50',
    '#8BC34A',
    '#CDDC39',
    '#FFC107',
    '#FF9800',
    '#795548',
    '#607D8B',
  ];

  // Generate teacher pool with realistic names
  final teachers = List.generate(15, (index) {
    final titles = ['Dr.', 'Prof.', 'Ms.', 'Mr.'];
    final title = titles[faker.randomGenerator.integer(titles.length)];
    return {
      'id': _uuid.v4(),
      'name': '$title ${faker.person.firstName()} ${faker.person.lastName()}',
    };
  });

  // Generate student pool with current user included
  final currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
  final students = [currentUserId, ...List.generate(49, (index) => _uuid.v4())];

  // Create core subject classes (5 classes)
  for (int i = 0; i < 5; i++) {
    final subject = coreSubjects[i % coreSubjects.length];
    final gradeLevel =
        gradeLevels[faker.randomGenerator.integer(gradeLevels.length)];
    final section = sections[faker.randomGenerator.integer(sections.length)];
    final teacher = teachers[i % teachers.length];
    final customColor =
        customColors[faker.randomGenerator.integer(customColors.length)];

    // Generate realistic student enrollment (15-25 students per class)
    final studentCount = faker.randomGenerator.integer(25, min: 15);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: '$subject $gradeLevel$section',
        subject: subject,
        description: _generateClassroomDescription(
          subject,
          ClassroomType.coreSubject,
        ),
        type: ClassroomType.coreSubject,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        customColor: faker.randomGenerator.boolean() ? customColor : null,
        gradeLevel: gradeLevel,
        section: section,
        isMainClass: i == 0, // First class is main class
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(200, min: 30)),
        ),
        isActive: true,
      ),
    );
  }

  // Create elective classes (2 classes)
  for (int i = 0; i < 2; i++) {
    final subject = electiveSubjects[i % electiveSubjects.length];
    final gradeLevel =
        gradeLevels[faker.randomGenerator.integer(gradeLevels.length)];
    final teacher = teachers[(i + 5) % teachers.length];
    final customColor =
        customColors[faker.randomGenerator.integer(customColors.length)];

    final studentCount = faker.randomGenerator.integer(20, min: 10);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: '$subject $gradeLevel',
        subject: subject,
        description: _generateClassroomDescription(
          subject,
          ClassroomType.elective,
        ),
        type: ClassroomType.elective,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        customColor: faker.randomGenerator.boolean() ? customColor : null,
        gradeLevel: gradeLevel,
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(150, min: 20)),
        ),
        isActive: true,
      ),
    );
  }

  // Create club classes (2 clubs)
  for (int i = 0; i < 2; i++) {
    final clubName = clubs[i % clubs.length];
    final customColor =
        customColors[faker.randomGenerator.integer(customColors.length)];

    // Some clubs have teacher supervisors, some have student leaders
    final hasTeacher = faker.randomGenerator.boolean();
    final teacher = hasTeacher ? teachers[(i + 7) % teachers.length] : null;
    final studentLeader = !hasTeacher
        ? {
            'id': students[faker.randomGenerator.integer(students.length)],
            'name': '${faker.person.firstName()} ${faker.person.lastName()}',
          }
        : null;

    final studentCount = faker.randomGenerator.integer(15, min: 8);
    final classStudents = <String>[];
    final shuffledStudents = List<String>.from(students)..shuffle();

    for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
      classStudents.add(shuffledStudents[j]);
    }

    classes.add(
      ClassModel(
        id: _uuid.v4(),
        name: clubName,
        description: _generateClassroomDescription(
          clubName,
          ClassroomType.club,
        ),
        type: ClassroomType.club,
        teacherId: teacher?['id'],
        teacherName: teacher?['name'],
        studentAdminId: studentLeader?['id'],
        studentAdminName: studentLeader?['name'],
        studentIds: classStudents,
        customColor: customColor,
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(100, min: 10)),
        ),
        isActive: true,
      ),
    );
  }

  // Create team classes (1 team)
  final teamName = teams[0];
  final customColor =
      customColors[faker.randomGenerator.integer(customColors.length)];
  final coach = teachers[9 % teachers.length];

  final studentCount = faker.randomGenerator.integer(12, min: 8);
  final classStudents = <String>[];
  final shuffledStudents = List<String>.from(students)..shuffle();

  for (int j = 0; j < studentCount && j < shuffledStudents.length; j++) {
    classStudents.add(shuffledStudents[j]);
  }

  classes.add(
    ClassModel(
      id: _uuid.v4(),
      name: teamName,
      description: _generateClassroomDescription(teamName, ClassroomType.team),
      type: ClassroomType.team,
      teacherId: coach['id']!,
      teacherName: coach['name']!,
      studentIds: classStudents,
      customColor: customColor,
      createdAt: DateTime.now().subtract(
        Duration(days: faker.randomGenerator.integer(80, min: 5)),
      ),
      isActive: true,
    ),
  );

  return classes;
}

/// Generate a realistic description for a classroom based on subject and type
String? _generateClassroomDescription(String? subject, ClassroomType type) {
  final faker = Faker();

  // Only generate descriptions for some classrooms (about 60%)
  if (faker.randomGenerator.integer(100) > 60) {
    return null;
  }

  switch (type) {
    case ClassroomType.coreSubject:
      return _generateCoreSubjectDescription(subject);
    case ClassroomType.elective:
      return _generateElectiveDescription(subject);
    case ClassroomType.club:
      return _generateClubDescription(subject);
    case ClassroomType.team:
      return _generateTeamDescription(subject);
    case ClassroomType.projectGroup:
      return _generateProjectDescription(subject);
    case ClassroomType.grade:
      return _generateGradeDescription(subject);
    case ClassroomType.custom:
      return 'Custom classroom for specialized learning activities';
  }
}

/// Generate description for core subjects
String _generateCoreSubjectDescription(String? subject) {
  final descriptions = {
    'Mathematics': [
      'Comprehensive mathematics course covering algebra, geometry, and calculus fundamentals',
      'Advanced mathematical concepts with practical problem-solving applications',
      'Interactive mathematics learning with real-world examples and exercises',
    ],
    'Physics': [
      'Explore the fundamental laws of nature through experiments and theoretical study',
      'Hands-on physics laboratory with modern equipment and demonstrations',
      'Understanding motion, energy, waves, and quantum mechanics principles',
    ],
    'Chemistry': [
      'Laboratory-based chemistry course with emphasis on practical experiments',
      'Organic and inorganic chemistry with molecular structure analysis',
      'Chemical reactions, bonding, and periodic table comprehensive study',
    ],
    'Biology': [
      'Life sciences exploration from cellular level to ecosystem dynamics',
      'Human anatomy, genetics, and evolution comprehensive curriculum',
      'Field studies and laboratory work in modern biology techniques',
    ],
    'English Literature': [
      'Critical analysis of classic and contemporary literary works',
      'Creative writing, poetry, and advanced composition skills development',
      'Literature appreciation with cultural and historical context',
    ],
    'History': [
      'World history from ancient civilizations to modern global events',
      'Critical thinking through historical analysis and primary sources',
      'Understanding cause and effect in historical developments',
    ],
    'Geography': [
      'Physical and human geography with map reading and spatial analysis',
      'Climate change, natural resources, and environmental studies',
      'Global cultures, urbanization, and demographic patterns',
    ],
  };

  final subjectDescriptions =
      descriptions[subject] ??
      ['Comprehensive academic course with theoretical and practical components'];

  final faker = Faker();
  return subjectDescriptions[faker.randomGenerator.integer(
    subjectDescriptions.length,
  )];
}

/// Generate description for elective subjects
String _generateElectiveDescription(String? subject) {
  final descriptions = {
    'Computer Science': [
      'Programming fundamentals with hands-on coding projects and algorithms',
      'Software development, web design, and computer systems architecture',
      'Data structures, artificial intelligence, and modern programming languages',
    ],
    'Art': [
      'Creative expression through various artistic mediums and techniques',
      'Art history, drawing, painting, and digital art creation',
      'Portfolio development and artistic skill enhancement',
    ],
    'Music': [
      'Music theory, composition, and performance skills development',
      'Ensemble playing, music history, and audio production',
      'Individual and group musical expression and appreciation',
    ],
    'Psychology': [
      'Human behavior, cognitive processes, and psychological research methods',
      'Understanding mental health, personality, and social psychology',
      'Practical applications of psychological principles in daily life',
    ],
    'Economics': [
      'Microeconomics, macroeconomics, and financial literacy',
      'Market analysis, economic policy, and global economic systems',
      'Business principles and entrepreneurship fundamentals',
    ],
  };

  final subjectDescriptions =
      descriptions[subject] ??
      ['Specialized elective course for advanced learning and skill development'];

  final faker = Faker();
  return subjectDescriptions[faker.randomGenerator.integer(
    subjectDescriptions.length,
  )];
}

/// Generate description for clubs
String _generateClubDescription(String? clubName) {
  final descriptions = {
    'Chess Club': [
      'Strategic thinking and chess mastery for all skill levels',
      'Tournament preparation and competitive chess training',
      'Chess theory, tactics, and friendly matches with peers',
    ],
    'Debate Club': [
      'Public speaking, argumentation, and critical thinking skills',
      'Competitive debate tournaments and speech competitions',
      'Research, rhetoric, and persuasive communication development',
    ],
    'Robotics Club': [
      'Build and program robots for competitions and exhibitions',
      'STEM learning through hands-on engineering and coding',
      'Innovation, teamwork, and technical problem-solving',
    ],
    'Drama Club': [
      'Theater arts, acting, and stage production experience',
      'Script writing, directing, and performance skills',
      'Creative expression through dramatic arts and storytelling',
    ],
    'Photography Club': [
      'Digital and film photography techniques and composition',
      'Photo editing, portfolio development, and visual storytelling',
      'Capturing moments and artistic expression through photography',
    ],
  };

  final clubDescriptions =
      descriptions[clubName] ??
      ['Student-led club for shared interests and collaborative activities'];

  final faker = Faker();
  return clubDescriptions[faker.randomGenerator.integer(
    clubDescriptions.length,
  )];
}

/// Generate description for teams
String _generateTeamDescription(String? teamName) {
  final descriptions = {
    'Basketball Team': [
      'Competitive basketball training and inter-school tournaments',
      'Teamwork, fitness, and basketball skills development',
      'School representation in regional basketball competitions',
    ],
    'Football Team': [
      'Football training, strategy, and competitive matches',
      'Physical fitness, team coordination, and sportsmanship',
      'Representing the school in football championships',
    ],
    'Cricket Team': [
      'Cricket skills development and competitive team play',
      'Batting, bowling, and fielding technique improvement',
      'Inter-school cricket tournaments and matches',
    ],
    'Volleyball Team': [
      'Volleyball training and competitive team participation',
      'Coordination, agility, and volleyball technique mastery',
      'School volleyball team for tournaments and competitions',
    ],
  };

  final teamDescriptions =
      descriptions[teamName] ??
      ['Competitive sports team representing the school in tournaments'];

  final faker = Faker();
  return teamDescriptions[faker.randomGenerator.integer(
    teamDescriptions.length,
  )];
}

/// Generate description for project groups
String _generateProjectDescription(String? projectName) {
  return 'Collaborative project group for hands-on learning and innovation';
}

/// Generate description for grade-level classrooms
String _generateGradeDescription(String? gradeName) {
  return 'Grade-level classroom for general activities and announcements';
}
