import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// 3x2 navigation grid for classroom features
class NavigationGrid extends StatelessWidget {
  /// ID of the current classroom
  final String classroomId;

  /// Callback when a navigation item is tapped
  final Function(String section) onNavigate;

  const NavigationGrid({
    super.key,
    required this.classroomId,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Define navigation items (3x2 grid)
    final navigationItems = [
      _NavigationItem(
        id: 'homework',
        title: 'Homework',
        icon: Symbols.assignment,
        color: const Color(0xFF2196F3),
      ),
      _NavigationItem(
        id: 'notes',
        title: 'Notes',
        icon: Symbols.note,
        color: const Color(0xFF4CAF50),
      ),
      _NavigationItem(
        id: 'quizzes',
        title: 'Quizzes',
        icon: Symbols.quiz,
        color: const Color(0xFF9C27B0),
      ),
      _NavigationItem(
        id: 'notices',
        title: 'Notices',
        icon: Symbols.campaign,
        color: const Color(0xFFFF9800),
      ),
      _NavigationItem(
        id: 'discussions',
        title: 'Discussions',
        icon: Symbols.forum,
        color: const Color(0xFF00BCD4),
      ),
      _NavigationItem(
        id: 'attendance',
        title: 'Attendance',
        icon: Symbols.how_to_reg,
        color: const Color(0xFF607D8B),
      ),
      _NavigationItem(
        id: 'digital_library',
        title: 'Digital Library',
        icon: Symbols.library_books,
        color: const Color(0xFF795548),
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
        childAspectRatio: 1.2,
      ),
      itemCount: navigationItems.length,
      itemBuilder: (context, index) {
        final item = navigationItems[index];
        return _buildNavigationCard(context, theme, item);
      },
    );
  }

  /// Build individual navigation card
  Widget _buildNavigationCard(
    BuildContext context,
    ThemeData theme,
    _NavigationItem item,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: () => onNavigate(item.id),
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                item.color.withValues(alpha: 0.1),
                item.color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: item.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(item.icon, size: 28.sp, color: item.color),
              ),

              SizedBox(height: 12.h),

              // Title
              Text(
                item.title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Data class for navigation items
class _NavigationItem {
  final String id;
  final String title;
  final IconData icon;
  final Color color;

  const _NavigationItem({
    required this.id,
    required this.title,
    required this.icon,
    required this.color,
  });
}
